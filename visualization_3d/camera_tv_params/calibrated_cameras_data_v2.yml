%YAML:1.0
---
nb_camera: 10
camera_0: # <PERSON><PERSON> (origin) (id = 0)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 995.51672008933781, 0., 1276.8370627997094, 0., 995.49674530837444, 875.73212391935715, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23884222078854914, -0.025551154902660082, -0.092122444224713107, 0.029872825629992526 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [ 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1., 0., 0., 0., 0., 1. ]

camera_1: # <PERSON><PERSON>eye (id = 2)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 997.58761451955877, 0., 1273.3487941464975, 0., 997.69851379720944, 873.43119455098179, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23519236516747918, -0.01564934717545418, -0.10770814991769842, 0.036587766862332408 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.99853094078197602,   0.012039110424951248,   0.052830106201272327,   -38.037582595420616,
          0.034902675324078307,  0.60287328221643222,   -0.79707315150167246,    752.5405958963197,
         -0.041445911213096258,  0.79774611588509903,    0.60156742850153888,    424.76010398173037,
          0.,                    0.,                     0.,                       1.                 ]

camera_2: # OMS Fisheye (id = 4)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 998.9556111718166, 0., 1281.6096705625323, 0., 998.49054516790761, 880.75642198515504, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.2364947388065943, -0.023857315006902597, -0.094579550921974551, 0.030329272826895366 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.63600200021297604, -0.35818727174195225,   0.68352273852970713, -930.82692168505628,
          0.30377827861924328,  0.93044363359348092,   0.20492291756777661,  -28.622603940061413,
         -0.70938016124235026,  0.07730797544508082,   0.70057352488397673,  163.8134580151935,
          0.,                   0.,                    0.,                     1.                 ]

camera_3: # OMS Fisheye (id = 6)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 988.11166397896204, 0., 1282.903403732015, 0., 988.05518461945883, 885.71654923503434, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ 0.23765127039718825, -0.027528968510007565, -0.095072556170993736, 0.031899969570357248 ]
   distortion_type: 1
   camera_group: 0
   img_width: 2592
   img_height: 1800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.54935695230258874,  0.33247798110928373,  -0.76659332832624238,   980.85351643880904,
         -0.18674514880264864,  0.94307689476043555,   0.2751948763470915,   -101.45086420756758,
          0.81445269252147978, -0.0080226333899242018, 0.58017449874843963,   267.41246918183811,
          0.,                   0.,                    0.,                      1.                 ]

camera_4: # RealSence (serial = 146222252273) looking to the table
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 645.87921142578102, 0., 651.13519287109398, 0., 645.03900146484398, 397.36730957031301, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.056801863014698001, 0.0684636235237122, -0.00044242519652470898, 4.961517333867e-06, -0.0226080249994993 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1280
   img_height: 800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.86419925571098211, -0.30448583348514513, -0.40055963805086625, 820.7195218202512,
         -0.14012026698082583,  0.61896827451966385, -0.77281601039275316, 824.44748582263287,
          0.48324523506166783,  0.72399354440837826,  0.49224728586878658, 323.37875703225075,
          0.,                   0.,                   0.,                    1.                 ]

camera_5: # RealSence (serial = 146222251797) looking to the door
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 645.33380126953102, 0., 653.04211425781295, 0., 644.45666503906295, 403.85299682617199, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.056624382734298699, 0.069789588451385498, 3.9113074308259996e-06, 0.00087557663209736304, -0.022849775850772899 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1280
   img_height: 800
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.79217115259275694, 0.13880538813658322,  0.59430457614266696,  59.941125479196494,
          0.37960547714893772, 0.65043128891587221, -0.65790487163249078, 805.4971216884943,
         -0.47787503253297298, 0.74677453265593474,  0.46256161822844793, 310.55878225206823,
          0.,                  0.,                   0.,                    1.                 ]

camera_6: # CCTV Pinhole (net-1: 169.254.0.3)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1390.1537518952414, 0., 972.44906180394787, 0., 1400.6892657075039, 554.695860247886, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41143196985045177, 0.24502508434849757, -4.3043597876293512e-05, -0.00022460504321067086, -0.092394239388181018 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.9819725948226935,    0.11265770924425839,    -0.15178294886126764,   740.39010810496939,
         -0.056023424143585569,  0.59344638203267974,     0.80292139565436305, -1586.7665708261823,
          0.18053032699359395,   0.79695020685119222,    -0.57643661302473459,  2173.0829375259345,
          0.,                    0.,                      0.,                      1.                 ]

camera_7: # CCTV Pinhole (net-2: 169.254.0.2)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1391.1703121086068, 0., 913.84060050517348, 0., 1401.8702849445347, 530.09274285945958, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.41176514203892389, 0.24101522308094739, 7.4994957863281606e-05, -9.3364968161397941e-06, -0.087169080293643744 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
         -0.98046503722837164,  -0.068751337039005017, 0.18428663659668076,  -821.87813385421077,
          0.10849469660495342,   0.59246532563686372,  0.79825668724202004, -1580.9630806275113,
         -0.1640646567099906,    0.80265689530044804, -0.57343238210383518,  2148.6785259619787,
          0.,                    0.,                   0.,                      1.                 ]

camera_8: # Anker Pinhole
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 1001.6491209548277, 0., 976.97579500562495, 0., 1002.7752138083686, 539.61129702327219, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 5
      dt: d
      data: [ -0.059047263426278383, -0.024620567753372138, -0.0072093767032458747, 0.00075840710330233403, 0.058591467863618678 ]
   distortion_type: 0
   camera_group: 0
   img_width: 1920
   img_height: 1080
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.99214242839198508,   0.057255528628843713, 0.11124390421900113,  -519.8633619047929,
          0.038290414504413235,  0.70752512810103374, -0.70565008131700513,   805.20104841853583,
         -0.1191102260157996,    0.70436496047649,     0.69977336081864205,   342.66195665712962,
          0.,                    0.,                   0.,                      1.                 ]

camera_9: # TFT – Helios (serial = 240600110)
   camera_matrix: !!opencv-matrix
      rows: 3
      cols: 3
      dt: d
      data: [ 368.05125427246094, 0., 324.86814880371094, 0., 368.05125427246094, 241.05181884765625, 0., 0., 1. ]
   distortion_vector: !!opencv-matrix
      rows: 1
      cols: 4
      dt: d
      data: [ -0.061244849604065385, -0.10712711033955256, 0.17144459510839152, -0.10552883821643241 ]
   distortion_type: 1
   camera_group: 0
   img_width: 640
   img_height: 480
   camera_pose_matrix: !!opencv-matrix
      rows: 4
      cols: 4
      dt: d
      data: [
          0.78006791628288941,   0.26313583880461705, -0.56767382916835385,  715.62283791389996,
         -0.097813263974443662,  0.94739921164439211,  0.30474136438338978, -105.10131934417777,
          0.61800211276073869,  -0.18219293101592823,  0.76477390417762592,  -70.807810206626726,
          0.,                    0.,                   0.,                     1.                 ]
