'''
###########################################################################
Gaze Dataset Preprocessing Pipeline - RUN ALL
###########################################################################
'''
import argparse

from utils.util import load_config
from scripts import prepare_gaze_data
from scripts import show_data_distribution
from scripts import show_gaze_vector


class GazeDatasetPreprocessingPipeline:
    def __init__(self, config_path, multiview=False):
        self.config_dict = load_config(config_path)
        self.multiview = multiview

    def _log_step_header(self, step_name):
        print("\n---------------------------------------------------------------------")
        print(step_name)
        print("---------------------------------------------------------------------\n")

    def prepare_gaze_data(self):
        self._log_step_header("Running: Gaze Dataset Preprocessing")
        prepare_gaze_data.run(self.config_dict)

    def show_data_distribution(self):
        self._log_step_header("Running: Show Data Distribution")
        show_data_distribution.run(self.config_dict)
        
    def show_gaze_vector(self):
        self._log_step_header("Running: Show Gaze Vectors")
        show_gaze_vector.run(self.config_dict)
        
    def run_all(self):
        self.prepare_gaze_data()
        self.show_data_distribution()
        self.show_gaze_vector()
        print("Gaze Dataset Preprocessing Pipeline completed.")


def main():
    """ Main function to run the gaze dataset preprocessing pipeline. """
    parser = argparse.ArgumentParser()
    parser.add_argument("-c", "--config-path", type=str, default="config.yaml")
    parser.add_argument("-m", "--matplotlib", action="store_true", help=".")
    parser.add_argument("-p", "--plotly", action="store_true", help="Show gaze data distribution.")
    parser.add_argument("-a", "--all", action="store_true", help="Show gaze data distribution.")
    args = parser.parse_args()
    
    # Create pipeline instance
    pipeline = GazeDatasetPreprocessingPipeline(args.config_path)

    # Run selected steps
    if args.matplotlib:
        pipeline.matplotlib()
    if args.plotly:
        pipeline.plotly()
    if args.run_all:
        pipeline.run_all()


if __name__ == '__main__':
    main()
