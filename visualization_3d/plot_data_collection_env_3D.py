import cv2
import numpy as np

from utils.plot_3d import *
from utils.transform import *


# Load camera and TV poses
params_path = "camera_tv_params/calibrated_cameras_data_intrinsics_extrinsics_tvs_final.yml"
params = cv2.FileStorage(params_path, cv2.FILE_STORAGE_READ)


def display_cameras_tvs_in_3D():
    # Setup figure
    ax = setup_figure()[1]

    cam_names = [
        "cam_0", "cam_2", "cam_4", "cam_6",     # OMS Fisheye
        "cam_8",                                # An<PERSON>
        "cctv_net_1", "cctv_net_2",             # CCTV Pinhole
        "rs_146222252273", "rs_146222251797",   # RealSense Pinhole
        "tof_240600110",                        # TOF
    ]
    
    # Display cameras
    for cam_name in cam_names:
        # Get camera pose
        cam_pose = params.getNode(cam_name).getNode("camera_pose_matrix").mat()
        
        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T
        
        # OMS Fisheye (origin)
        if cam_name == "cam_0":
            cam_color = (1.0, 0.0, 0.0) # red
            cam_label = f"0: Fisheye OMS (origin)"
            
        # OMS Fisheye
        if cam_name in ["cam_2", "cam_4", "cam_6"]:
            cam_color = (0.95, 0.5, 0.07) # orange
            cam_label = f"{cam_name.split('_')[-1]}: Fisheye OMS"

        # Anker Pinhole
        elif cam_name in ["cam_8"]:
            cam_color = (1.0, 1.0, 0.0) # green
            cam_label = f"{cam_name.split('_')[-1]}: Pinhole Anker"

        # CCTV Pinhole
        elif cam_name in ["cctv_net_1", "cctv_net_2"]:
            cam_color = (0.0, 0.0, 1.0) # blue
            cam_label = f"{cam_name.split('_')[-1]}: Pinhole CCTV"
            
        # RealSense Pinhole
        elif cam_name in ["rs_146222252273", "rs_146222251797"]:
            cam_color = (0.3, 0.8, 1.0) # light blue
            cam_label = f"{cam_name[-2:]}: Pinhole RealSense"
        
        # TFT – Helios (Depth)
        elif cam_name in ["tof_240600110"]:
            cam_color = (0.0, 1.0, 0.0) # green
            cam_label = f"TFT - Helios (Depth)"
        
        # Add cameras
        add_camera_to_subplot(
            ax,
            R=cam_R,
            tvec=cam_tvec,
            cam_color=cam_color,
            label=cam_label
        )

    tv_names = ["tv_1", "tv_2", "tv_3"]
    
    # Display cameras
    for tv_name in tv_names:
        # Get TV dimensions
        width_mm = params.getNode(tv_name).getNode("width_mm").real()
        height_mm = params.getNode(tv_name).getNode("height_mm").real()
        monitor_mm = (width_mm, height_mm)
        
        # Get camera pose
        tv_pose = params.getNode(tv_name).getNode("camera_pose_matrix").mat()
        
        # Get rotation and translation
        tv_R = tv_pose[0:3, 0:3]
        tv_tvec = np.asarray([tv_pose[0:3, 3]]).T
        
        # Only screen with partially displayed fields
        if tv_name == "tv_1":
            tv_label = "TV-1: Left"
            
        elif tv_name == "tv_2":
            tv_label = "TV-2: Middle"
            
        elif tv_name == "tv_3":
            tv_label = "TV-3: Right"
            
        # Add TVs
        add_tv_screen_to_subplot(
            ax,
            R=tv_R,
            tvec=tv_tvec,
            monitor_mm=monitor_mm,
            label=tv_label
        )

    # Show the figure
    show_figure(save=True)


if __name__ == "__main__":
    display_cameras_tvs_in_3D()
