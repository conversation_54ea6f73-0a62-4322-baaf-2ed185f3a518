import cv2
import numpy as np

from utils.transform import *
from utils.plot_3d_plotly import PlotlyVisualizer3D


# Load camera and TV poses
params_path = "camera_tv_params/calibrated_cameras_data_intrinsics_extrinsics_tvs_final.yml"
params = cv2.FileStorage(params_path, cv2.FILE_STORAGE_READ)


def display_cameras_tvs_in_3D_plotly():
    """
    Display cameras and TV screens in an interactive 3D visualization using Plotly.
    Provides better interactivity and visual quality compared to matplotlib.
    """
    # Create the Plotly visualizer
    viz = PlotlyVisualizer3D(title="Interactive 3D Camera-TV Calibration Visualization")

    cam_names = [
        "cam_0", "cam_2", "cam_4", "cam_6",     # OMS Fisheye
        "cam_8",                                # Anker Pinhole
        "cctv_net_1", "cctv_net_2",             # CCTV Pinhole
        "rs_146222252273", "rs_146222251797",   # RealSense Pinhole
        "tof_240600110",                        # TOF
    ]

    # Get cam_0 position for camera focusing
    cam_0_pose = params.getNode("cam_0").getNode("camera_pose_matrix").mat()
    cam_0_position = cam_0_pose[0:3, 3]  # Extract translation vector

    # Display cameras
    for cam_name in cam_names:
        # Get camera pose
        cam_pose = params.getNode(cam_name).getNode("camera_pose_matrix").mat()

        # Get rotation and translation
        cam_R = cam_pose[0:3, 0:3]
        cam_tvec = np.asarray([cam_pose[0:3, 3]]).T

        # OMS Fisheye (origin)
        if cam_name == "cam_0":
            cam_color = 'red'
            cam_label = f"0: Fisheye OMS (origin)"

        # OMS Fisheye
        elif cam_name in ["cam_2", "cam_4", "cam_6"]:
            cam_color = 'orange'
            cam_label = f"{cam_name.split('_')[-1]}: Fisheye OMS"

        # Anker Pinhole
        elif cam_name in ["cam_8"]:
            cam_color = 'yellow'
            cam_label = f"{cam_name.split('_')[-1]}: Pinhole Anker"

        # CCTV Pinhole
        elif cam_name in ["cctv_net_1", "cctv_net_2"]:
            cam_color = 'blue'
            cam_label = f"{cam_name.split('_')[-1]}: Pinhole CCTV"

        # RealSense Pinhole
        elif cam_name in ["rs_146222252273", "rs_146222251797"]:
            cam_color = 'lightblue'
            cam_label = f"{cam_name[-2:]}: Pinhole RealSense"

        # TFT – Helios (Depth)
        elif cam_name in ["tof_240600110"]:
            cam_color = 'green'
            cam_label = f"TFT - Helios (Depth)"

        else:
            cam_color = 'gray'
            cam_label = f"{cam_name}: Unknown Camera"

        # Add camera to visualization
        viz.add_camera(
            R=cam_R,
            tvec=cam_tvec,
            cam_color=cam_color,
            label=cam_label,
            cam_size=40
        )

    tv_names = ["tv_1", "tv_2", "tv_3"]

    # Display TVs
    for tv_name in tv_names:
        # Get TV dimensions
        width_mm = params.getNode(tv_name).getNode("width_mm").real()
        height_mm = params.getNode(tv_name).getNode("height_mm").real()
        monitor_mm = (width_mm, height_mm)

        # Get TV pose
        tv_pose = params.getNode(tv_name).getNode("camera_pose_matrix").mat()

        # Get rotation and translation
        tv_R = tv_pose[0:3, 0:3]
        tv_tvec = np.asarray([tv_pose[0:3, 3]]).T

        # Define TV specifications and colors
        if tv_name == "tv_1":
            tv_label = "TV-1: Left"
            screen_color = 'rgba(255, 200, 200, 0.3)'  # Light red

        elif tv_name == "tv_2":
            tv_label = "TV-2: Middle"
            screen_color = 'rgba(200, 255, 200, 0.3)'  # Light green

        elif tv_name == "tv_3":
            tv_label = "TV-3: Right"
            screen_color = 'rgba(200, 200, 255, 0.3)'  # Light blue

        else:
            tv_label = f"TV: {tv_name}"
            screen_color = 'rgba(200, 200, 200, 0.3)'  # Light gray

        # Add TV screen to visualization
        viz.add_tv_screen(
            R=tv_R,
            tvec=tv_tvec,
            monitor_mm=monitor_mm,
            label=tv_label,
            screen_color=screen_color
        )

    # Set camera to focus on cam_0 (origin camera)
    viz.set_camera_focus(cam_0_position, distance=3800, elevation_angle=30)

    # Show the interactive visualization
    html_filename = "interactive_calibration_result.html"
    viz.show(save_html=True, filename=html_filename)
    
    # Optionally save as static image
    svg_filename = "calibration_result_plotly.svg"
    viz.save_image(svg_filename)
    
    print("\n" + "="*60)
    print("🎉 Interactive 3D Visualization Complete!")
    print("="*60)
    print(f"✅ Interactive HTML saved to: outputs/{html_filename}")
    print(f"✅ Static SVG saved to: outputs/{svg_filename}")
    print("\n📋 Features of the Plotly visualization:")
    print("   • Interactive rotation, zoom, and pan")
    print("   • Hover information for cameras and screens")
    print("   • Better visual quality with WebGL rendering")
    print("   • Professional appearance with proper lighting")
    print("   • Export capabilities (HTML, PNG, SVG, PDF)")
    print("   • Responsive design that works in web browsers")
    print("\n🔧 Controls:")
    print("   • Left click + drag: Rotate view")
    print("   • Right click + drag: Pan view")
    print("   • Scroll wheel: Zoom in/out")
    print("   • Double click: Reset view")
    print("   • Click legend items: Show/hide elements")
    print("="*60)


if __name__ == "__main__":
    display_cameras_tvs_in_3D_plotly()
